import { APP_ROUTES, AppRoutesEnum } from '@/routes/appRoutes'

import DictionaryIcon from '@/assets/icons/dictionary-icon.svg'
import HealthIcon from '@/assets/icons/health-icon-full.svg'
import HomeIcon from '@/assets/icons/home-icon.svg'
import MedicineIcon from '@/assets/icons/medicine-icon-full.svg'
import NewsIcon from '@/assets/icons/news-icon-full.svg'
import { SvgProps } from 'react-native-svg'
export interface HomeFeaturedCategory {
  title: string
  icon: React.ComponentType<SvgProps>
  url: string
  authRequired?: boolean
}

export const APP_TABS = {
  [AppRoutesEnum.MEDICAL_DICTIONARY]: {
    name: APP_ROUTES.MEDICAL_DICTIONARY.tabName,
    path: APP_ROUTES.MEDICAL_DICTIONARY.path,
    keyTranslate: 'MES-644',
    icon: DictionaryIcon,
  },
  [AppRoutesEnum.MEDICAL_HANDBOOK]: {
    name: APP_ROUTES.MEDICAL_HANDBOOK.tabName,
    path: APP_ROUTES.MEDICAL_HANDBOOK.path,
    keyTranslate: 'MES-33',
    icon: HealthIcon,
  },
  [AppRoutesEnum.HOME]: {
    name: APP_ROUTES.HOME.name,
    path: APP_ROUTES.HOME.path,
    keyTranslate: 'MES-37',
    icon: HomeIcon,
  },
  [AppRoutesEnum.PRODUCTS]: {
    name: APP_ROUTES.PRODUCTS.tabName,
    path: APP_ROUTES.PRODUCTS.path,
    keyTranslate: 'MES-586',
    icon: MedicineIcon,
  },
  [AppRoutesEnum.POSTS]: {
    name: APP_ROUTES.POSTS.tabName,
    path: APP_ROUTES.POSTS.path,
    keyTranslate: 'MES-19',
    icon: NewsIcon,
  },
  // [AppRoutesEnum.CHAT]: {
  //   name: APP_ROUTES.CHAT.tabName,
  //   path: APP_ROUTES.CHAT.path,
  //   keyTranslate: 'Chat',
  //   icon: MessageIcon,
  // },
}
